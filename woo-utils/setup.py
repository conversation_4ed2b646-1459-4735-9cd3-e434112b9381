from setuptools import setup, find_packages

setup(
    name='woo_utils',
    version='0.0.1',
    author='pugwoo',
    author_email='<EMAIL>',
    description='my common utils',
    long_description=open('README.md', encoding='utf-8').read(),
    long_description_content_type='text/markdown',
    url='https://code.pugwoo.com/pugwoo/py-woo-utils',
    packages=find_packages(),
    install_requires=[
        # 在此列出模块依赖的第三方库，例如：
        # 'requests>=2.25.1',
    ],
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',  # 根据你选择的许可证调整
        'Operating System :: OS Independent',
    ],
    python_requires='>=3.8',
)