#!/usr/bin/env python
"""
Basic unit tests for torch_woo_utils package that don't require PyTorch.
"""

import unittest
import sys
import os

# Add the parent directory to the path to import torch_woo_utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch_woo_utils


class TestBasicFunctionality(unittest.TestCase):
    """Test cases for basic package functionality"""
    
    def test_package_import(self):
        """Test that the package can be imported"""
        self.assertIsNotNone(torch_woo_utils)
    
    def test_version_function(self):
        """Test the get_version function"""
        version = torch_woo_utils.get_version()
        self.assertIsInstance(version, str)
        self.assertEqual(version, '0.0.1')
    
    def test_device_function(self):
        """Test the get_device function"""
        device = torch_woo_utils.get_device()
        self.assertIsInstance(device, str)
        self.assertEqual(device, 'cpu')
    
    def test_torch_availability(self):
        """Test the is_torch_available function"""
        torch_available = torch_woo_utils.is_torch_available()
        self.assertIsInstance(torch_available, bool)
        # Should be False since PyTorch is not installed in this environment
        self.assertFalse(torch_available)
    
    def test_set_device_function(self):
        """Test the set_device function"""
        # This should not raise an error
        torch_woo_utils.set_device('cuda')
        # Since it's a no-op in the basic version, device should still be 'cpu'
        self.assertEqual(torch_woo_utils.get_device(), 'cpu')
    
    def test_all_exports(self):
        """Test that __all__ contains expected functions"""
        expected_functions = ['get_version', 'get_device', 'set_device', 'is_torch_available']
        self.assertEqual(torch_woo_utils.__all__, expected_functions)
        
        # Test that all functions in __all__ are actually available
        for func_name in expected_functions:
            self.assertTrue(hasattr(torch_woo_utils, func_name))
    
    def test_package_metadata(self):
        """Test package metadata"""
        self.assertEqual(torch_woo_utils.__version__, '0.0.1')
        self.assertEqual(torch_woo_utils.__author__, 'pugwoo')
        self.assertEqual(torch_woo_utils.__email__, '<EMAIL>')


if __name__ == '__main__':
    unittest.main()
