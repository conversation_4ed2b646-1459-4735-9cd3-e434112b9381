#!/usr/bin/env python
"""
Unit tests for torch_woo_utils.models module.
"""

import unittest
import torch
import torch.nn as nn
import numpy as np
import sys
import os

# Add the parent directory to the path to import torch_woo_utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from torch_woo_utils.models import SimpleMLP, SimpleRNN, count_parameters, model_summary


class TestSimpleMLP(unittest.TestCase):
    """Test cases for the SimpleMLP model"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.input_size = 10
        self.hidden_sizes = [64, 32]
        self.output_size = 1
        
    def test_mlp_creation(self):
        """Test MLP model creation"""
        model = SimpleMLP(
            input_size=self.input_size,
            hidden_sizes=self.hidden_sizes,
            output_size=self.output_size
        )
        
        self.assertIsInstance(model, nn.Module)
        self.assertEqual(model.input_size, self.input_size)
        self.assertEqual(model.hidden_sizes, self.hidden_sizes)
        self.assertEqual(model.output_size, self.output_size)
    
    def test_mlp_forward_pass(self):
        """Test MLP forward pass"""
        model = SimpleMLP(
            input_size=self.input_size,
            hidden_sizes=self.hidden_sizes,
            output_size=self.output_size
        )
        
        # Create dummy input
        batch_size = 32
        x = torch.randn(batch_size, self.input_size)
        
        # Forward pass
        output = model(x)
        
        # Check output shape
        expected_shape = (batch_size, self.output_size)
        self.assertEqual(output.shape, expected_shape)
        
        # Check output is not NaN
        self.assertFalse(torch.isnan(output).any())
    
    def test_mlp_different_activations(self):
        """Test MLP with different activation functions"""
        activations = ['relu', 'tanh', 'sigmoid']
        
        for activation in activations:
            with self.subTest(activation=activation):
                model = SimpleMLP(
                    input_size=self.input_size,
                    hidden_sizes=self.hidden_sizes,
                    output_size=self.output_size,
                    activation=activation
                )
                
                x = torch.randn(16, self.input_size)
                output = model(x)
                
                self.assertEqual(output.shape, (16, self.output_size))
                self.assertFalse(torch.isnan(output).any())


class TestSimpleRNN(unittest.TestCase):
    """Test cases for the SimpleRNN model"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.input_size = 5
        self.hidden_size = 32
        self.num_layers = 2
        self.output_size = 1
        self.seq_len = 10
        self.batch_size = 16
        
    def test_rnn_creation(self):
        """Test RNN model creation"""
        model = SimpleRNN(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            output_size=self.output_size,
            rnn_type='LSTM'
        )
        
        self.assertIsInstance(model, nn.Module)
        self.assertEqual(model.input_size, self.input_size)
        self.assertEqual(model.hidden_size, self.hidden_size)
        self.assertEqual(model.num_layers, self.num_layers)
        self.assertEqual(model.output_size, self.output_size)
        self.assertEqual(model.rnn_type, 'LSTM')
    
    def test_rnn_forward_pass(self):
        """Test RNN forward pass"""
        model = SimpleRNN(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            output_size=self.output_size,
            rnn_type='LSTM'
        )
        
        # Create dummy input (batch_size, seq_len, input_size)
        x = torch.randn(self.batch_size, self.seq_len, self.input_size)
        
        # Forward pass
        output = model(x)
        
        # Check output shape
        expected_shape = (self.batch_size, self.output_size)
        self.assertEqual(output.shape, expected_shape)
        
        # Check output is not NaN
        self.assertFalse(torch.isnan(output).any())
    
    def test_rnn_different_types(self):
        """Test RNN with different RNN types"""
        rnn_types = ['RNN', 'LSTM', 'GRU']
        
        for rnn_type in rnn_types:
            with self.subTest(rnn_type=rnn_type):
                model = SimpleRNN(
                    input_size=self.input_size,
                    hidden_size=self.hidden_size,
                    num_layers=self.num_layers,
                    output_size=self.output_size,
                    rnn_type=rnn_type
                )
                
                x = torch.randn(self.batch_size, self.seq_len, self.input_size)
                output = model(x)
                
                self.assertEqual(output.shape, (self.batch_size, self.output_size))
                self.assertFalse(torch.isnan(output).any())


class TestUtilityFunctions(unittest.TestCase):
    """Test cases for utility functions"""
    
    def test_count_parameters(self):
        """Test parameter counting function"""
        model = SimpleMLP(input_size=10, hidden_sizes=[64, 32], output_size=1)
        param_count = count_parameters(model)
        
        # Should be a positive integer
        self.assertIsInstance(param_count, int)
        self.assertGreater(param_count, 0)
        
        # Manual calculation for verification
        # Layer 1: 10 * 64 + 64 = 704
        # Layer 2: 64 * 32 + 32 = 2080  
        # Layer 3: 32 * 1 + 1 = 33
        # Total: 704 + 2080 + 33 = 2817
        expected_params = (10 * 64 + 64) + (64 * 32 + 32) + (32 * 1 + 1)
        self.assertEqual(param_count, expected_params)
    
    def test_model_summary(self):
        """Test model summary function"""
        model = SimpleMLP(input_size=10, hidden_sizes=[64, 32], output_size=1)
        summary = model_summary(model, (10,))
        
        # Should return a string
        self.assertIsInstance(summary, str)
        
        # Should contain key information
        self.assertIn("SimpleMLP", summary)
        self.assertIn("parameters", summary)
        self.assertIn("Input shape", summary)
        self.assertIn("Output shape", summary)


if __name__ == '__main__':
    unittest.main()
