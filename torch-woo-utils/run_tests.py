#!/usr/bin/env python
"""
Test runner script for torch_woo_utils package.

Usage:
    python run_tests.py              # Run all tests
    python run_tests.py models       # Run only models tests
    python run_tests.py -v           # Run with verbose output
"""

import sys
import unittest
import os

def main():
    """Main test runner function"""
    # Add current directory to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    # Parse command line arguments
    verbose = '-v' in sys.argv or '--verbose' in sys.argv

    # Determine which tests to run
    if len(sys.argv) > 1 and not sys.argv[1].startswith('-'):
        # Run specific test module
        test_module = sys.argv[1]
        if not test_module.startswith('test_'):
            test_module = f'test_{test_module}'

        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(f'tests.{test_module}')
    else:
        # Run all tests
        loader = unittest.TestLoader()
        suite = loader.discover('tests', pattern='test_*.py')

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)

    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)

if __name__ == '__main__':
    main()
