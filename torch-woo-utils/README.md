# torch-woo-utils

PyTorch-based deep learning utilities package.

## 安装说明

1. 项目本身的依赖：

```bash
pip install -r requirements.txt
```

2. 安装到本地library

```bash
pip install .

# 目前发现这个行不通(开发者模式): pip install -e .
# 先不弄这个开发者模式
```

如果要安装到python或conda虚拟环境，则在这个虚拟环境console中，进入到当前这个文件目录，然后执行上面的命令即可。

3. 升级代码到本地library

```bash
pip install --upgrade .

# 说明：如果用的py notebook jupyter server，jupyter server要重启才能生效
```

## 功能模块

### 深度学习工具
- 神经网络模型构建
- 训练和推理工具
- 模型评估和可视化

### 数据处理
- 数据预处理工具
- 数据加载器
- 数据增强

### 可视化
- 训练过程可视化
- 模型结构可视化
- 结果展示

## 环境变量

为了更简化方法的入参，增设以下环境变量，请根据实际情况修改：

```
PY_CHART_URL=画图的工具url，例如 https://branch.pugwoo.com
TORCH_DEVICE=默认设备，例如 cuda:0 或 cpu
```

## 使用示例

```python
import torch_woo_utils as twu

# 示例代码将在后续版本中添加
```

## 测试

运行所有测试：

```bash
python run_tests.py
```

运行特定模块测试：

```bash
python run_tests.py module_name
```

## 版本历史

- v0.0.1: 初始版本，基础框架搭建
