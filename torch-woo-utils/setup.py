from setuptools import setup, find_packages

setup(
    name='torch_woo_utils',
    version='0.0.1',
    author='pugwoo',
    author_email='<EMAIL>',
    description='PyTorch-based deep learning utilities',
    long_description=open('README.md', encoding='utf-8').read(),
    long_description_content_type='text/markdown',
    url='https://code.pugwoo.com/pugwoo/py-woo-utils',
    packages=find_packages(),
    install_requires=[
        # 在此列出模块依赖的第三方库，例如：
        # 'requests>=2.25.1',
    ],
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Developers',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ],
    python_requires='>=3.8',
)
