# 负责深度学习模型相关的工作
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, List
import numpy as np


class SimpleMLP(nn.Module):
    """
    简单的多层感知机模型
    """
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, 
                 dropout_rate: float = 0.1, activation: str = 'relu'):
        """
        初始化MLP模型
        :param input_size: 输入特征维度
        :param hidden_sizes: 隐藏层维度列表
        :param output_size: 输出维度
        :param dropout_rate: Dropout比率
        :param activation: 激活函数类型 ('relu', 'tanh', 'sigmoid')
        """
        super(SimpleMLP, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size
        self.dropout_rate = dropout_rate
        
        # 构建网络层
        layers = []
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            layers.append(nn.Linear(prev_size, hidden_size))
            
            # 添加激活函数
            if activation == 'relu':
                layers.append(nn.ReLU())
            elif activation == 'tanh':
                layers.append(nn.Tanh())
            elif activation == 'sigmoid':
                layers.append(nn.Sigmoid())
            
            # 添加Dropout
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            
            prev_size = hidden_size
        
        # 输出层
        layers.append(nn.Linear(prev_size, output_size))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        :param x: 输入张量
        :return: 输出张量
        """
        return self.network(x)


class SimpleRNN(nn.Module):
    """
    简单的RNN模型，用于时间序列预测
    """
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 1,
                 output_size: int = 1, rnn_type: str = 'LSTM', dropout: float = 0.0):
        """
        初始化RNN模型
        :param input_size: 输入特征维度
        :param hidden_size: 隐藏层维度
        :param num_layers: RNN层数
        :param output_size: 输出维度
        :param rnn_type: RNN类型 ('RNN', 'LSTM', 'GRU')
        :param dropout: Dropout比率
        """
        super(SimpleRNN, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.rnn_type = rnn_type
        
        # 选择RNN类型
        if rnn_type == 'LSTM':
            self.rnn = nn.LSTM(input_size, hidden_size, num_layers, 
                              batch_first=True, dropout=dropout)
        elif rnn_type == 'GRU':
            self.rnn = nn.GRU(input_size, hidden_size, num_layers,
                             batch_first=True, dropout=dropout)
        else:
            self.rnn = nn.RNN(input_size, hidden_size, num_layers,
                             batch_first=True, dropout=dropout)
        
        # 输出层
        self.fc = nn.Linear(hidden_size, output_size)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        :param x: 输入张量 (batch_size, seq_len, input_size)
        :return: 输出张量 (batch_size, output_size)
        """
        # RNN前向传播
        rnn_out, _ = self.rnn(x)
        
        # 取最后一个时间步的输出
        last_output = rnn_out[:, -1, :]
        
        # 通过全连接层
        output = self.fc(last_output)
        
        return output


def count_parameters(model: nn.Module) -> int:
    """
    计算模型的参数数量
    :param model: PyTorch模型
    :return: 参数总数
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def model_summary(model: nn.Module, input_size: Tuple[int, ...]) -> str:
    """
    生成模型摘要信息
    :param model: PyTorch模型
    :param input_size: 输入张量大小（不包括batch维度）
    :return: 模型摘要字符串
    """
    summary = []
    summary.append(f"Model: {model.__class__.__name__}")
    summary.append(f"Total parameters: {count_parameters(model):,}")
    
    # 创建示例输入来获取输出形状
    with torch.no_grad():
        dummy_input = torch.randn(1, *input_size)
        try:
            output = model(dummy_input)
            summary.append(f"Input shape: {tuple(dummy_input.shape)}")
            summary.append(f"Output shape: {tuple(output.shape)}")
        except Exception as e:
            summary.append(f"Could not determine output shape: {e}")
    
    return "\n".join(summary)
