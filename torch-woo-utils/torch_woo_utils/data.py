# 负责数据处理相关的工作
import torch
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from typing import Tuple, Optional, List, Union
from sklearn.preprocessing import StandardScaler, MinMaxScaler


class TimeSeriesDataset(Dataset):
    """
    时间序列数据集类
    """
    def __init__(self, data: np.ndarray, sequence_length: int, target_length: int = 1):
        """
        初始化时间序列数据集
        :param data: 时间序列数据 (n_samples, n_features)
        :param sequence_length: 输入序列长度
        :param target_length: 目标序列长度
        """
        self.data = data
        self.sequence_length = sequence_length
        self.target_length = target_length
        
        # 计算有效样本数量
        self.n_samples = len(data) - sequence_length - target_length + 1
        
        if self.n_samples <= 0:
            raise ValueError("数据长度不足以创建有效的序列")
    
    def __len__(self) -> int:
        return self.n_samples
    
    def __getitem__(self, idx: int) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        # 输入序列
        x = self.data[idx:idx + self.sequence_length]
        # 目标序列
        y = self.data[idx + self.sequence_length:idx + self.sequence_length + self.target_length]
        
        return torch.FloatTensor(x), torch.FloatTensor(y)


class TabularDataset(Dataset):
    """
    表格数据集类
    """
    def __init__(self, features: np.ndarray, targets: np.ndarray):
        """
        初始化表格数据集
        :param features: 特征数据
        :param targets: 目标数据
        """
        self.features = features
        self.targets = targets
        
        if len(features) != len(targets):
            raise ValueError("特征和目标数据长度不匹配")
    
    def __len__(self) -> int:
        return len(self.features)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return torch.FloatTensor(self.features[idx]), torch.FloatTensor(self.targets[idx])


def create_time_series_dataset(df: pd.DataFrame, target_column: str, 
                              sequence_length: int, target_length: int = 1,
                              feature_columns: Optional[List[str]] = None,
                              scaler_type: str = 'standard') -> Tuple[TimeSeriesDataset, object]:
    """
    从DataFrame创建时间序列数据集
    :param df: 输入DataFrame
    :param target_column: 目标列名
    :param sequence_length: 输入序列长度
    :param target_length: 目标序列长度
    :param feature_columns: 特征列名列表，如果为None则使用除目标列外的所有列
    :param scaler_type: 缩放器类型 ('standard', 'minmax', 'none')
    :return: 数据集和缩放器
    """
    # 选择特征列
    if feature_columns is None:
        feature_columns = [col for col in df.columns if col != target_column]
    
    # 准备数据
    if feature_columns:
        data = df[feature_columns + [target_column]].values
    else:
        data = df[[target_column]].values
    
    # 数据缩放
    scaler = None
    if scaler_type == 'standard':
        scaler = StandardScaler()
        data = scaler.fit_transform(data)
    elif scaler_type == 'minmax':
        scaler = MinMaxScaler()
        data = scaler.fit_transform(data)
    
    # 创建数据集
    dataset = TimeSeriesDataset(data, sequence_length, target_length)
    
    return dataset, scaler


def create_tabular_dataset(df: pd.DataFrame, target_column: str,
                          feature_columns: Optional[List[str]] = None,
                          scaler_type: str = 'standard') -> Tuple[TabularDataset, object]:
    """
    从DataFrame创建表格数据集
    :param df: 输入DataFrame
    :param target_column: 目标列名
    :param feature_columns: 特征列名列表，如果为None则使用除目标列外的所有列
    :param scaler_type: 缩放器类型 ('standard', 'minmax', 'none')
    :return: 数据集和缩放器
    """
    # 选择特征列
    if feature_columns is None:
        feature_columns = [col for col in df.columns if col != target_column]
    
    # 准备数据
    features = df[feature_columns].values
    targets = df[target_column].values
    
    # 特征缩放
    scaler = None
    if scaler_type == 'standard':
        scaler = StandardScaler()
        features = scaler.fit_transform(features)
    elif scaler_type == 'minmax':
        scaler = MinMaxScaler()
        features = scaler.fit_transform(features)
    
    # 创建数据集
    dataset = TabularDataset(features, targets)
    
    return dataset, scaler


def train_val_split(dataset: Dataset, val_ratio: float = 0.2, 
                   random_seed: Optional[int] = None) -> Tuple[Dataset, Dataset]:
    """
    将数据集分割为训练集和验证集
    :param dataset: 原始数据集
    :param val_ratio: 验证集比例
    :param random_seed: 随机种子
    :return: 训练集和验证集
    """
    if random_seed is not None:
        torch.manual_seed(random_seed)
    
    dataset_size = len(dataset)
    val_size = int(dataset_size * val_ratio)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    return train_dataset, val_dataset


def create_dataloader(dataset: Dataset, batch_size: int = 32, shuffle: bool = True,
                     num_workers: int = 0, **kwargs) -> DataLoader:
    """
    创建数据加载器
    :param dataset: 数据集
    :param batch_size: 批次大小
    :param shuffle: 是否打乱数据
    :param num_workers: 工作进程数
    :param kwargs: 其他DataLoader参数
    :return: 数据加载器
    """
    return DataLoader(
        dataset, 
        batch_size=batch_size, 
        shuffle=shuffle, 
        num_workers=num_workers,
        **kwargs
    )


def normalize_data(data: np.ndarray, method: str = 'standard') -> Tuple[np.ndarray, object]:
    """
    数据标准化
    :param data: 输入数据
    :param method: 标准化方法 ('standard', 'minmax')
    :return: 标准化后的数据和缩放器
    """
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'minmax':
        scaler = MinMaxScaler()
    else:
        raise ValueError(f"Unsupported normalization method: {method}")
    
    normalized_data = scaler.fit_transform(data)
    return normalized_data, scaler


def denormalize_data(data: np.ndarray, scaler: object) -> np.ndarray:
    """
    数据反标准化
    :param data: 标准化后的数据
    :param scaler: 缩放器
    :return: 反标准化后的数据
    """
    return scaler.inverse_transform(data)
