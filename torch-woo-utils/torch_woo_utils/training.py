# 负责模型训练相关的工作
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, List, Optional, Callable, Any
import time
import numpy as np
from collections import defaultdict


class Trainer:
    """
    通用的模型训练器
    """
    def __init__(self, model: nn.Module, device: str = 'cpu'):
        """
        初始化训练器
        :param model: 要训练的模型
        :param device: 训练设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.history = defaultdict(list)
    
    def train_epoch(self, dataloader: DataLoader, criterion: nn.Module, 
                   optimizer: optim.Optimizer) -> Dict[str, float]:
        """
        训练一个epoch
        :param dataloader: 数据加载器
        :param criterion: 损失函数
        :param optimizer: 优化器
        :return: 训练指标字典
        """
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(dataloader):
            data, target = data.to(self.device), target.to(self.device)
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 前向传播
            output = self.model(data)
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        return {'loss': avg_loss}
    
    def validate_epoch(self, dataloader: DataLoader, criterion: nn.Module) -> Dict[str, float]:
        """
        验证一个epoch
        :param dataloader: 验证数据加载器
        :param criterion: 损失函数
        :return: 验证指标字典
        """
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for data, target in dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                output = self.model(data)
                loss = criterion(output, target)
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        return {'val_loss': avg_loss}
    
    def fit(self, train_dataloader: DataLoader, val_dataloader: Optional[DataLoader],
            criterion: nn.Module, optimizer: optim.Optimizer, epochs: int,
            scheduler: Optional[Any] = None, early_stopping_patience: Optional[int] = None,
            verbose: bool = True) -> Dict[str, List[float]]:
        """
        训练模型
        :param train_dataloader: 训练数据加载器
        :param val_dataloader: 验证数据加载器
        :param criterion: 损失函数
        :param optimizer: 优化器
        :param epochs: 训练轮数
        :param scheduler: 学习率调度器
        :param early_stopping_patience: 早停耐心值
        :param verbose: 是否打印训练过程
        :return: 训练历史
        """
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_dataloader, criterion, optimizer)
            
            # 验证
            val_metrics = {}
            if val_dataloader is not None:
                val_metrics = self.validate_epoch(val_dataloader, criterion)
            
            # 学习率调度
            if scheduler is not None:
                if hasattr(scheduler, 'step'):
                    if 'val_loss' in val_metrics:
                        scheduler.step(val_metrics['val_loss'])
                    else:
                        scheduler.step()
            
            # 记录历史
            for key, value in train_metrics.items():
                self.history[key].append(value)
            for key, value in val_metrics.items():
                self.history[key].append(value)
            
            # 早停检查
            if early_stopping_patience is not None and 'val_loss' in val_metrics:
                if val_metrics['val_loss'] < best_val_loss:
                    best_val_loss = val_metrics['val_loss']
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= early_stopping_patience:
                        if verbose:
                            print(f"Early stopping at epoch {epoch+1}")
                        break
            
            # 打印进度
            if verbose:
                epoch_time = time.time() - start_time
                metrics_str = " - ".join([f"{k}: {v:.4f}" for k, v in {**train_metrics, **val_metrics}.items()])
                print(f"Epoch {epoch+1}/{epochs} - {epoch_time:.2f}s - {metrics_str}")
        
        return dict(self.history)


def create_optimizer(model: nn.Module, optimizer_name: str = 'adam', 
                    learning_rate: float = 0.001, **kwargs) -> optim.Optimizer:
    """
    创建优化器
    :param model: 模型
    :param optimizer_name: 优化器名称
    :param learning_rate: 学习率
    :param kwargs: 其他优化器参数
    :return: 优化器实例
    """
    if optimizer_name.lower() == 'adam':
        return optim.Adam(model.parameters(), lr=learning_rate, **kwargs)
    elif optimizer_name.lower() == 'sgd':
        return optim.SGD(model.parameters(), lr=learning_rate, **kwargs)
    elif optimizer_name.lower() == 'rmsprop':
        return optim.RMSprop(model.parameters(), lr=learning_rate, **kwargs)
    elif optimizer_name.lower() == 'adamw':
        return optim.AdamW(model.parameters(), lr=learning_rate, **kwargs)
    else:
        raise ValueError(f"Unsupported optimizer: {optimizer_name}")


def create_scheduler(optimizer: optim.Optimizer, scheduler_name: str = 'step',
                    **kwargs) -> Optional[Any]:
    """
    创建学习率调度器
    :param optimizer: 优化器
    :param scheduler_name: 调度器名称
    :param kwargs: 调度器参数
    :return: 调度器实例
    """
    if scheduler_name.lower() == 'step':
        return optim.lr_scheduler.StepLR(optimizer, **kwargs)
    elif scheduler_name.lower() == 'cosine':
        return optim.lr_scheduler.CosineAnnealingLR(optimizer, **kwargs)
    elif scheduler_name.lower() == 'plateau':
        return optim.lr_scheduler.ReduceLROnPlateau(optimizer, **kwargs)
    elif scheduler_name.lower() == 'exponential':
        return optim.lr_scheduler.ExponentialLR(optimizer, **kwargs)
    elif scheduler_name.lower() == 'none':
        return None
    else:
        raise ValueError(f"Unsupported scheduler: {scheduler_name}")
