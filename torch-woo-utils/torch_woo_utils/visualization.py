# 负责可视化相关的工作
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import torch
import torch.nn as nn


def plot_training_history(history: Dict[str, List[float]], figsize: Tuple[int, int] = (12, 4),
                         save_path: Optional[str] = None) -> None:
    """
    绘制训练历史
    :param history: 训练历史字典
    :param figsize: 图形大小
    :param save_path: 保存路径
    """
    # 分离训练和验证指标
    train_metrics = {k: v for k, v in history.items() if not k.startswith('val_')}
    val_metrics = {k: v for k, v in history.items() if k.startswith('val_')}
    
    n_metrics = len(train_metrics)
    if n_metrics == 0:
        print("No training metrics found in history")
        return
    
    fig, axes = plt.subplots(1, n_metrics, figsize=figsize)
    if n_metrics == 1:
        axes = [axes]
    
    for i, (metric_name, values) in enumerate(train_metrics.items()):
        ax = axes[i]
        epochs = range(1, len(values) + 1)
        
        # 绘制训练指标
        ax.plot(epochs, values, 'b-', label=f'Training {metric_name}')
        
        # 绘制验证指标（如果存在）
        val_metric_name = f'val_{metric_name}'
        if val_metric_name in val_metrics:
            ax.plot(epochs, val_metrics[val_metric_name], 'r-', 
                   label=f'Validation {metric_name}')
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel(metric_name.capitalize())
        ax.set_title(f'{metric_name.capitalize()} History')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def plot_predictions(y_true: np.ndarray, y_pred: np.ndarray, 
                    dates: Optional[List] = None, figsize: Tuple[int, int] = (12, 6),
                    title: str = "Predictions vs Actual", save_path: Optional[str] = None) -> None:
    """
    绘制预测结果对比
    :param y_true: 真实值
    :param y_pred: 预测值
    :param dates: 日期列表
    :param figsize: 图形大小
    :param title: 图形标题
    :param save_path: 保存路径
    """
    plt.figure(figsize=figsize)
    
    x_axis = dates if dates is not None else range(len(y_true))
    
    plt.plot(x_axis, y_true, 'b-', label='Actual', linewidth=2)
    plt.plot(x_axis, y_pred, 'r--', label='Predicted', linewidth=2)
    
    plt.xlabel('Time' if dates is not None else 'Index')
    plt.ylabel('Value')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 如果有日期，旋转x轴标签
    if dates is not None:
        plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def plot_residuals(y_true: np.ndarray, y_pred: np.ndarray, 
                  figsize: Tuple[int, int] = (12, 4), save_path: Optional[str] = None) -> None:
    """
    绘制残差图
    :param y_true: 真实值
    :param y_pred: 预测值
    :param figsize: 图形大小
    :param save_path: 保存路径
    """
    residuals = y_true - y_pred
    
    fig, axes = plt.subplots(1, 2, figsize=figsize)
    
    # 残差散点图
    axes[0].scatter(y_pred, residuals, alpha=0.6)
    axes[0].axhline(y=0, color='r', linestyle='--')
    axes[0].set_xlabel('Predicted Values')
    axes[0].set_ylabel('Residuals')
    axes[0].set_title('Residuals vs Predicted')
    axes[0].grid(True, alpha=0.3)
    
    # 残差直方图
    axes[1].hist(residuals, bins=30, alpha=0.7, edgecolor='black')
    axes[1].set_xlabel('Residuals')
    axes[1].set_ylabel('Frequency')
    axes[1].set_title('Residuals Distribution')
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def plot_correlation_matrix(df: pd.DataFrame, figsize: Tuple[int, int] = (10, 8),
                           save_path: Optional[str] = None) -> None:
    """
    绘制相关性矩阵热图
    :param df: 数据框
    :param figsize: 图形大小
    :param save_path: 保存路径
    """
    plt.figure(figsize=figsize)
    
    correlation_matrix = df.corr()
    
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    
    plt.title('Feature Correlation Matrix')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def plot_feature_importance(feature_names: List[str], importance_scores: np.ndarray,
                           figsize: Tuple[int, int] = (10, 6), top_n: Optional[int] = None,
                           save_path: Optional[str] = None) -> None:
    """
    绘制特征重要性图
    :param feature_names: 特征名称列表
    :param importance_scores: 重要性分数
    :param figsize: 图形大小
    :param top_n: 显示前N个重要特征
    :param save_path: 保存路径
    """
    # 创建DataFrame并排序
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importance_scores
    }).sort_values('importance', ascending=True)
    
    # 选择前N个特征
    if top_n is not None:
        importance_df = importance_df.tail(top_n)
    
    plt.figure(figsize=figsize)
    plt.barh(importance_df['feature'], importance_df['importance'])
    plt.xlabel('Importance Score')
    plt.title('Feature Importance')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def plot_model_architecture(model: nn.Module, input_size: Tuple[int, ...],
                           save_path: Optional[str] = None) -> None:
    """
    可视化模型架构（简单文本形式）
    :param model: PyTorch模型
    :param input_size: 输入大小
    :param save_path: 保存路径
    """
    print("Model Architecture:")
    print("=" * 50)
    print(model)
    print("=" * 50)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # 尝试获取输出形状
    try:
        with torch.no_grad():
            dummy_input = torch.randn(1, *input_size)
            output = model(dummy_input)
            print(f"Input shape: {tuple(dummy_input.shape)}")
            print(f"Output shape: {tuple(output.shape)}")
    except Exception as e:
        print(f"Could not determine output shape: {e}")


def set_style(style: str = 'seaborn') -> None:
    """
    设置绘图样式
    :param style: 样式名称
    """
    plt.style.use(style)
    sns.set_palette("husl")
