#!/usr/bin/env python
"""
Example usage of torch_woo_utils package.
"""

import torch
import numpy as np
import pandas as pd
from torch_woo_utils import (
    SimpleMLP, SimpleRNN, Trainer, 
    create_time_series_dataset, create_dataloader,
    plot_training_history, get_version, get_device
)

def main():
    """Main example function"""
    print(f"torch_woo_utils version: {get_version()}")
    print(f"Default device: {get_device()}")
    print("-" * 50)
    
    # Example 1: Simple MLP for regression
    print("Example 1: Simple MLP for regression")
    
    # Create a simple MLP model
    model = SimpleMLP(
        input_size=10,
        hidden_sizes=[64, 32],
        output_size=1,
        dropout_rate=0.1,
        activation='relu'
    )
    
    print(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Create dummy data
    X = torch.randn(1000, 10)
    y = torch.sum(X[:, :3], dim=1, keepdim=True) + 0.1 * torch.randn(1000, 1)
    
    # Test forward pass
    with torch.no_grad():
        output = model(X[:5])
        print(f"Sample output shape: {output.shape}")
    
    print("-" * 50)
    
    # Example 2: Simple RNN for time series
    print("Example 2: Simple RNN for time series")
    
    # Create a simple RNN model
    rnn_model = SimpleRNN(
        input_size=1,
        hidden_size=32,
        num_layers=2,
        output_size=1,
        rnn_type='LSTM'
    )
    
    print(f"RNN model created with {sum(p.numel() for p in rnn_model.parameters())} parameters")
    
    # Create time series data
    time_series = np.sin(np.linspace(0, 4*np.pi, 200)) + 0.1 * np.random.randn(200)
    df = pd.DataFrame({
        'date': pd.date_range('2023-01-01', periods=200, freq='D'),
        'value': time_series
    })
    
    # Create dataset
    dataset, scaler = create_time_series_dataset(
        df, 
        target_column='value',
        sequence_length=10,
        target_length=1,
        scaler_type='standard'
    )
    
    print(f"Time series dataset created with {len(dataset)} samples")
    
    # Create dataloader
    dataloader = create_dataloader(dataset, batch_size=32, shuffle=True)
    
    # Test RNN forward pass
    for batch_x, batch_y in dataloader:
        with torch.no_grad():
            rnn_output = rnn_model(batch_x)
            print(f"RNN batch input shape: {batch_x.shape}")
            print(f"RNN batch output shape: {rnn_output.shape}")
        break
    
    print("-" * 50)
    
    # Example 3: Training setup (without actual training)
    print("Example 3: Training setup")
    
    # Create trainer
    trainer = Trainer(model, device='cpu')
    
    # Create optimizer and criterion
    from torch_woo_utils.training import create_optimizer
    import torch.nn as nn
    
    optimizer = create_optimizer(model, 'adam', learning_rate=0.001)
    criterion = nn.MSELoss()
    
    print("Trainer, optimizer, and criterion created successfully")
    print("Ready for training!")
    
    print("-" * 50)
    print("Example completed successfully!")

if __name__ == '__main__':
    main()
